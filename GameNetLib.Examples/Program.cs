using System.Collections.Concurrent;
using System.Text;
using DotNetty.Codecs.Http.WebSockets.Extensions;
using GameNetLib.Core;
using GameNetLib.Core.Interfaces;
using GameNetLib.Core.Tcp;
using GameNetLib.Core.WebSocket;
using GameNetLib.Logging;
using GameNetLib.Utils;
using GameNetLib.Proto;
using Google.Protobuf;

namespace GameNetLib.Examples;

/// <summary>
/// 示例程序主入口
/// </summary>
class Program
{
    private static ILoggerFactory? _loggerFactory;
    private static ILogger? _logger;

    static async Task Main(string[] args)
    {
        // 初始化日志
        _loggerFactory = new DefaultLoggerFactory(
            minimumLevel: LogLevel.Debug,
            writeToConsole: true,
            writeToFile: true,
            logFilePath: "logs/gamenetlib-examples.log"
        );
        _logger = _loggerFactory.CreateLogger<Program>();

        _logger.LogInformation("GameNetLib Examples Starting...");


        // await RunTcpServerExample(args);
        //
        // await RunTCPClientExample(args);
        
        RunWebSocketServerExample(args);

        for (int i = 0; i < 100; i++) {
            RunWebSocketClientExample(i);
        }

        await Task.Delay(10000000);
        
    }
    

    /// <summary>
    /// 运行服务器示例
    /// </summary>
    private static void RunTcpServerExample(string[] args)
    {
        // var protocol = args.Length > 1 ? args[1] : "tcp";
        // var port = args.Length > 2 && int.TryParse(args[2], out var p) ? p : 8080;
        //
        // _logger!.LogInformation("Starting {Protocol} server on port {Port}", protocol, port);


        ServerConnectionManager serverConnectionManager = new ServerConnectionManager();
        TcpServer<Packet> server = TcpServer<Packet>.start(serverConnectionManager, 1111);
        
        //周期性tick
        Task.Run(async () =>
        {
            while (true)
            {
                serverConnectionManager.tick();
                await Task.Delay(1000);
            }
        });
        
        
        // 模拟服务器运行
        // Console.WriteLine("Press any key to stop the server...");
        // Console.ReadKey();
    }

    /// <summary>
    /// 运行客户端示例
    /// </summary>
    private static async Task RunTCPClientExample(string[] args)
    {

        Client client = new Client(1);
        TcpClient<Packet>.connectTo("127.0.0.1", 1111, client);
        await Task.Delay(10000);
    }
    
    //run websocket server example
    private static void RunWebSocketServerExample(string[] args)
    {
        
        ServerConnectionManager serverConnectionManager = new ServerConnectionManager();
        WSServer<Packet> server = WSServer<Packet>.Start(serverConnectionManager, 1111);
        //周期性tick
        Task.Run(async () =>
        {
            while (true)
            {
                serverConnectionManager.tick();
                await Task.Delay(1000);
            }
        });
    }
    
    //run websocket client example
    private static void RunWebSocketClientExample(int id)
    {
        Client client = new Client(id);
        WSClient<Packet> wsClient = new WSClient<Packet>();
        wsClient.connectTo("ws://127.0.0.1:1111/ws",  client, id);


        Task.Run(async () =>
        {
            while (true)
            {
                await Task.Delay(1000);
                client.tick();
            }
        });
    }
}

public class Client : IConnectionManager<Packet>
{

    private int _id;
    public Client(int id)
    {
        _id = id;
    }

    public ClientConnection _connection { get; set; }

    public long AddConnection(INetChannel<Packet> channel)
    {
        _connection = new ClientConnection(channel);
        
        //say hello
        Packet packet = new Packet();
        packet._PTCode = 1001;
        packet._sn = 1;
        
        packet._Data = Encoding.UTF8.GetBytes("Hello World");
        
        ((INetConnection<Packet>)_connection).SendAsync(packet);
        
        return _connection.Id;
    }

    public INetConnection<Packet>? GetConnection(long connectionId)
    {
        return _connection;
    }

    public IEnumerable<INetConnection<Packet>> GetAllConnections()
    {
        //返回当前连接
        if (_connection != null)
        {
            return new List<INetConnection<Packet>> { _connection };
        }
        return new List<INetConnection<Packet>>();
    }

    public int GetConnectionCount()
    {
        return _connection != null ? 1 : 0;
    }
    
    Random random = new Random();
    public void tick()
    {
        if (_connection != null)
        {
            //活跃的连接，发送心跳包
            Packet packet = new Packet();
            packet._PTCode = 1000;
            packet._sn = _id;
            packet._Data = new byte[random.Next(10, 65000)];
            ((INetConnection<Packet>)_connection).SendAsync(packet);
        }
    }
}

public class ClientConnection : AbstractNetConnection<Packet>
{
    //队列，用于存放Packet
    private readonly ConcurrentQueue<Packet> _messageQueue = new();
    
    public ClientConnection(INetChannel<Packet> channel) : base(channel)
    {
        
    }

    public override void SubAddPacket(Packet packet)
    {
        var size = 0;
        if (packet._Data != null)
        {
            size = packet._Data.Length;
        }
        System.Console.WriteLine("收到消息：" + packet._sn + " size : " + size + " from " + Channel.RemoteAddress());
        _messageQueue.Enqueue(packet);
    }
}

public class ServerConnectionManager : IConnectionManager<Packet>
{
    //并发dictionary，用于存放连接
    private readonly ConcurrentDictionary<long, ClientConnection> _connections = new();
    
    public long AddConnection(INetChannel<Packet> channel)
    {
        ClientConnection connection = new ClientConnection(channel);
        _connections.TryAdd(connection.Id, connection);
        return connection.Id;
    }

    public INetConnection<Packet>? GetConnection(long connectionId)
    {
        return _connections.TryGetValue(connectionId, out var connection) ? connection : null;
    }

    public IEnumerable<INetConnection<Packet>> GetAllConnections()
    {
        return _connections.Values;
    }

    public int GetConnectionCount()
    {
        return _connections.Count;
    }

    private int _sn;

    public void tick()
    {
        var connectionsToRemove = new List<long>();

        foreach (var kvp in _connections)
        {
            var connection = kvp.Value;
            try
            {
                if (!((INetConnection<Packet>)connection).IsActive())
                {
                    connectionsToRemove.Add(connection.Id);
                }
                else
                {
                    //活跃的连接，发送心跳包
                    Packet packet = new Packet();
                    packet._PTCode = 1000;
                    packet._sn = _sn++;
                    ((INetConnection<Packet>)connection).SendAsync(packet);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing connection {connection.Id}: {ex.Message}");
                connectionsToRemove.Add(connection.Id);
            }
        }

        // 清理不活跃的连接
        foreach (var connectionId in connectionsToRemove)
        {
            if (_connections.TryRemove(connectionId, out var removedConnection))
            {
                try
                {
                    removedConnection.Dispose();
                    Console.WriteLine($"Removed inactive connection: {connectionId}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing connection {connectionId}: {ex.Message}");
                }
            }
        }
    }
}
