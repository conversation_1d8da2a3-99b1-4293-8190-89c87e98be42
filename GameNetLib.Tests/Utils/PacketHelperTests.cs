using FluentAssertions;
using GameNetLib.Utils;
using GameNetLib.Proto;
using Xunit;

namespace GameNetLib.Tests.Utils;

/// <summary>
/// 包工具测试
/// </summary>
public class PacketHelperTests
{
    [Fact]
    public void CreateHeartbeatPacket_ShouldCreateValidPacket()
    {
        // Arrange
        var clientId = "test_client";

        // Act
        var packet = PacketHelper.CreateHeartbeatPacket(clientId);

        // Assert
        packet.Should().NotBeNull();
        packet.Cmd.Should().Be(PacketCommands.HEARTBEAT);
        packet.Payload.Should().NotBeNull();
        packet.Payload.Length.Should().BeGreaterThan(0);

        // 验证包格式
        PacketHelper.IsValidPacket(packet).Should().BeTrue();
    }

    [Fact]
    public void CreateAuthPacket_ShouldCreateValidPacket()
    {
        // Arrange
        var token = "test_token";
        var clientId = "test_client";
        var version = 1;

        // Act
        var packet = PacketHelper.CreateAuthPacket(token, clientId, version);

        // Assert
        packet.Should().NotBeNull();
        packet.Cmd.Should().Be(PacketCommands.AUTH);
        packet.Payload.Should().NotBeNull();
        packet.Payload.Length.Should().BeGreaterThan(0);

        // 验证包格式
        PacketHelper.IsValidPacket(packet).Should().BeTrue();
    }

    [Fact]
    public void CreateErrorPacket_ShouldCreateValidPacket()
    {
        // Arrange
        var errorCode = 1001;
        var errorMessage = "Test error";

        // Act
        var packet = PacketHelper.CreateErrorPacket(errorCode, errorMessage);

        // Assert
        packet.Should().NotBeNull();
        packet.Cmd.Should().Be(PacketCommands.ERROR);
        packet.Payload.Should().NotBeNull();
        packet.Payload.Length.Should().BeGreaterThan(0);

        // 验证包格式
        PacketHelper.IsValidPacket(packet).Should().BeTrue();
    }

    [Fact]
    public void ParseHeartbeatPacket_ShouldParseCorrectly()
    {
        // Arrange
        var clientId = "test_client";
        var packet = PacketHelper.CreateHeartbeatPacket(clientId);

        // Act
        var parsed = PacketHelper.ParseHeartbeatPacket(packet);

        // Assert
        parsed.Should().NotBeNull();
        parsed!.ClientId.Should().Be(clientId);
        parsed.Timestamp.Should().BeGreaterThan(0);
    }

    [Fact]
    public void ParseAuthPacket_ShouldParseCorrectly()
    {
        // Arrange
        var token = "test_token";
        var clientId = "test_client";
        var version = 1;
        var packet = PacketHelper.CreateAuthPacket(token, clientId, version);

        // Act
        var parsed = PacketHelper.ParseAuthPacket(packet);

        // Assert
        parsed.Should().NotBeNull();
        parsed!.Token.Should().Be(token);
        parsed.ClientId.Should().Be(clientId);
        parsed.Version.Should().Be(version);
    }

    [Fact]
    public void ParseErrorPacket_ShouldParseCorrectly()
    {
        // Arrange
        var errorCode = 1001;
        var errorMessage = "Test error";
        var packet = PacketHelper.CreateErrorPacket(errorCode, errorMessage);

        // Act
        var parsed = PacketHelper.ParseErrorPacket(packet);

        // Assert
        parsed.Should().NotBeNull();
        parsed!.ErrorCode.Should().Be(errorCode);
        parsed.ErrorMessage.Should().Be(errorMessage);
    }

    [Fact]
    public void ParseHeartbeatPacket_WithWrongCommand_ShouldReturnNull()
    {
        // Arrange
        var packet = PacketHelper.CreateErrorPacket(1001, "error");

        // Act
        var parsed = PacketHelper.ParseHeartbeatPacket(packet);

        // Assert
        parsed.Should().BeNull();
    }

    [Fact]
    public void IsValidPacket_WithValidPacket_ShouldReturnTrue()
    {
        // Arrange
        var packet = PacketHelper.CreateHeartbeatPacket("test");

        // Act
        var isValid = PacketHelper.IsValidPacket(packet);

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void IsValidPacket_WithNullPacket_ShouldReturnFalse()
    {
        // Act
        var isValid = PacketHelper.IsValidPacket(null);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void IsValidPacket_WithInvalidCommand_ShouldReturnFalse()
    {
        // Arrange
        var packet = new PbPacket
        {
            Cmd = 0, // 无效命令
            Payload = Google.Protobuf.ByteString.CopyFrom(new byte[] { 1, 2, 3 })
        };

        // Act
        var isValid = PacketHelper.IsValidPacket(packet);

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void GetCommandName_ShouldReturnCorrectNames()
    {
        // Act & Assert
        PacketHelper.GetCommandName(PacketCommands.HEARTBEAT).Should().Be("HEARTBEAT");
        PacketHelper.GetCommandName(PacketCommands.AUTH).Should().Be("AUTH");
        PacketHelper.GetCommandName(PacketCommands.AUTH_RESPONSE).Should().Be("AUTH_RESPONSE");
        PacketHelper.GetCommandName(PacketCommands.ERROR).Should().Be("ERROR");
        PacketHelper.GetCommandName(PacketCommands.RESPONSE).Should().Be("RESPONSE");
        PacketHelper.GetCommandName(9999).Should().Be("UNKNOWN");
    }

    [Fact]
    public void GetPacketDescription_ShouldReturnCorrectDescription()
    {
        // Arrange
        var packet = PacketHelper.CreateHeartbeatPacket("test");

        // Act
        var description = PacketHelper.GetPacketDescription(packet);

        // Assert
        description.Should().Contain("HEARTBEAT");
        description.Should().Contain(PacketCommands.HEARTBEAT.ToString());
        description.Should().Contain("PayloadSize");
    }

    [Fact]
    public void CalculatePacketSize_ShouldReturnCorrectSize()
    {
        // Arrange
        var packet = PacketHelper.CreateHeartbeatPacket("test");

        // Act
        var size = PacketHelper.CalculatePacketSize(packet);

        // Assert
        size.Should().BeGreaterThan(0);
        size.Should().Be(packet.CalculateSize());
    }

    [Fact]
    public void CreateCustomPacket_WithByteArray_ShouldCreateValidPacket()
    {
        // Arrange
        var cmd = 1000;
        var payload = new byte[] { 1, 2, 3, 4, 5 };

        // Act
        var packet = PacketHelper.CreateCustomPacket(cmd, payload);

        // Assert
        packet.Should().NotBeNull();
        packet.Cmd.Should().Be(cmd);
        packet.Payload.ToByteArray().Should().Equal(payload);
    }

    [Fact]
    public void CreateCustomPacket_WithProtobufMessage_ShouldCreateValidPacket()
    {
        // Arrange
        var cmd = 1000;
        var heartbeat = new HeartbeatPacket
        {
            ClientId = "test",
            Timestamp = 123456789
        };

        // Act
        var packet = PacketHelper.CreateCustomPacket(cmd, heartbeat);

        // Assert
        packet.Should().NotBeNull();
        packet.Cmd.Should().Be(cmd);
        packet.Payload.Should().NotBeNull();
        packet.Payload.Length.Should().BeGreaterThan(0);

        // 验证可以解析回原始消息
        var parsed = HeartbeatPacket.Parser.ParseFrom(packet.Payload);
        parsed.ClientId.Should().Be(heartbeat.ClientId);
        parsed.Timestamp.Should().Be(heartbeat.Timestamp);
    }

    [Theory]
    [InlineData(ErrorCodes.UNKNOWN_ERROR)]
    [InlineData(ErrorCodes.AUTH_FAILED)]
    [InlineData(ErrorCodes.INVALID_PACKET)]
    [InlineData(ErrorCodes.PACKET_TOO_LARGE)]
    [InlineData(ErrorCodes.RATE_LIMITED)]
    [InlineData(ErrorCodes.INTERNAL_ERROR)]
    [InlineData(ErrorCodes.CONNECTION_TIMEOUT)]
    [InlineData(ErrorCodes.UNSUPPORTED_VERSION)]
    public void ErrorCodes_ShouldBeValidConstants(int errorCode)
    {
        // Assert
        errorCode.Should().BeGreaterThan(0);
        errorCode.Should().BeGreaterOrEqualTo(1000); // 错误码应该从1000开始
    }

    [Theory]
    [InlineData(PacketCommands.HEARTBEAT)]
    [InlineData(PacketCommands.AUTH)]
    [InlineData(PacketCommands.AUTH_RESPONSE)]
    [InlineData(PacketCommands.ERROR)]
    [InlineData(PacketCommands.RESPONSE)]
    public void PacketCommands_ShouldBeValidConstants(int command)
    {
        // Assert
        command.Should().BeGreaterThan(0);
        command.Should().BeLessThan(PacketCommands.USER_DEFINED_START); // 系统命令应该小于用户自定义起始值
    }
}
