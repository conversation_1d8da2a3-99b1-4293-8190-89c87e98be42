using DotNetty.Buffers;
using System;

namespace GameNetLib.Utils;

/// <summary>
/// 字节序处理工具类
/// </summary>
public static class ByteOrderHelper
{
    /// <summary>
    /// 字节序枚举
    /// </summary>
    public enum ByteOrder
    {
        /// <summary>
        /// 大端序 (Big Endian) - 网络字节序
        /// </summary>
        BigEndian,
        
        /// <summary>
        /// 小端序 (Little Endian) - Intel x86/x64 架构
        /// </summary>
        LittleEndian
    }

    /// <summary>
    /// 获取系统默认字节序
    /// </summary>
    public static ByteOrder SystemByteOrder => BitConverter.IsLittleEndian ? ByteOrder.LittleEndian : ByteOrder.BigEndian;

    #region Int32 操作

    /// <summary>
    /// 按指定字节序写入 Int32
    /// </summary>
    public static void WriteInt(this IByteBuffer buffer, int value, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            buffer.WriteIntLE(value);
        }
        else
        {
            buffer.WriteInt(value);
        }
    }

    /// <summary>
    /// 按指定字节序读取 Int32
    /// </summary>
    public static int ReadInt(this IByteBuffer buffer, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            return buffer.ReadIntLE();
        }
        else
        {
            return buffer.ReadInt();
        }
    }

    #endregion

    #region Int64 操作

    /// <summary>
    /// 按指定字节序写入 Int64
    /// </summary>
    public static void WriteLong(this IByteBuffer buffer, long value, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            buffer.WriteLongLE(value);
        }
        else
        {
            buffer.WriteLong(value);
        }
    }

    /// <summary>
    /// 按指定字节序读取 Int64
    /// </summary>
    public static long ReadLong(this IByteBuffer buffer, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            return buffer.ReadLongLE();
        }
        else
        {
            return buffer.ReadLong();
        }
    }

    #endregion

    #region Int16 操作

    /// <summary>
    /// 按指定字节序写入 Int16
    /// </summary>
    public static void WriteShort(this IByteBuffer buffer, short value, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            buffer.WriteShortLE(value);
        }
        else
        {
            buffer.WriteShort(value);
        }
    }

    /// <summary>
    /// 按指定字节序读取 Int16
    /// </summary>
    public static short ReadShort(this IByteBuffer buffer, ByteOrder byteOrder)
    {
        if (byteOrder == ByteOrder.LittleEndian)
        {
            return buffer.ReadShortLE();
        }
        else
        {
            return buffer.ReadShort();
        }
    }

    #endregion

    #region 字节数组转换

    /// <summary>
    /// 将 Int32 转换为指定字节序的字节数组
    /// </summary>
    public static byte[] ToBytes(int value, ByteOrder byteOrder)
    {
        var bytes = BitConverter.GetBytes(value);
        if (ShouldReverse(byteOrder))
        {
            Array.Reverse(bytes);
        }
        return bytes;
    }

    /// <summary>
    /// 将 Int64 转换为指定字节序的字节数组
    /// </summary>
    public static byte[] ToBytes(long value, ByteOrder byteOrder)
    {
        var bytes = BitConverter.GetBytes(value);
        if (ShouldReverse(byteOrder))
        {
            Array.Reverse(bytes);
        }
        return bytes;
    }

    /// <summary>
    /// 从指定字节序的字节数组转换为 Int32
    /// </summary>
    public static int ToInt32(byte[] bytes, ByteOrder byteOrder)
    {
        if (bytes.Length != 4)
            throw new ArgumentException("Byte array must be 4 bytes long for Int32");

        if (ShouldReverse(byteOrder))
        {
            var reversed = new byte[4];
            Array.Copy(bytes, reversed, 4);
            Array.Reverse(reversed);
            return BitConverter.ToInt32(reversed, 0);
        }
        return BitConverter.ToInt32(bytes, 0);
    }

    /// <summary>
    /// 从指定字节序的字节数组转换为 Int64
    /// </summary>
    public static long ToInt64(byte[] bytes, ByteOrder byteOrder)
    {
        if (bytes.Length != 8)
            throw new ArgumentException("Byte array must be 8 bytes long for Int64");

        if (ShouldReverse(byteOrder))
        {
            var reversed = new byte[8];
            Array.Copy(bytes, reversed, 8);
            Array.Reverse(reversed);
            return BitConverter.ToInt64(reversed, 0);
        }
        return BitConverter.ToInt64(bytes, 0);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 判断是否需要反转字节序
    /// </summary>
    private static bool ShouldReverse(ByteOrder targetByteOrder)
    {
        return (BitConverter.IsLittleEndian && targetByteOrder == ByteOrder.BigEndian) ||
               (!BitConverter.IsLittleEndian && targetByteOrder == ByteOrder.LittleEndian);
    }

    #endregion

    #region 常量定义

    /// <summary>
    /// 协议使用的字节序（可以根据需要修改）
    /// </summary>
    public static readonly ByteOrder ProtocolByteOrder = ByteOrder.BigEndian;

    #endregion
}
