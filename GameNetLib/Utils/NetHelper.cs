using System.Net;
using DotNetty.Codecs.Http;
using DotNetty.Common.Utilities;
using DotNetty.Transport.Channels;

namespace GameNetLib.Utils;

public class NetHelper
{
    public static readonly AttributeKey<object> IPKey = AttributeKey<object>.ValueOf("IP");
    public static readonly AttributeKey<object> PortKey = AttributeKey<object>.ValueOf("Port");

    public static void SetIP(IChannelHandlerContext ctx, String ip)
    {
        ctx.GetAttribute(IPKey).Set(ip);
    }
    
    public static void InitIPInfo(IChannelHandlerContext ctx, IFullHttpRequest req)
    {
        var ipAttr = ctx.Channel.GetAttribute(IPKey);
        if (ipAttr.Get() == null)
        {
            string? ip = GetIpByFullHttpRequest(req);
            if (!string.IsNullOrEmpty(ip))
            {
                ipAttr.Set(ip);
            }
            else
            {
                var (ipAdd, port) = GetIpAddressAndPort(ctx.Channel.RemoteAddress);
                
                ctx.GetAttribute(IPKey).Set(ipAdd);
                ctx.GetAttribute(PortKey).Set(port);
            }
        }
    }
    
    public static (string ipAddress, int port) GetIpAddressAndPort(EndPoint remoteAddress)
    {
        if (remoteAddress is IPEndPoint ipEndPoint)
        {
            return (ipEndPoint.Address.ToString(), ipEndPoint.Port);
        }
        else
        {
            throw new ArgumentException("The remote address is not an IPEndPoint.");
        }
    }

    public static string? GetIpByFullHttpRequest(IFullHttpRequest request)
    {
        if (request?.Headers == null)
        {
            return null;
        }

        var headers = request.Headers;
        var ip = headers.Get(AsciiString.Cached("x-forwarded-for"),null)?.ToString();
    
        if (!string.IsNullOrWhiteSpace(ip))
        {
            ip = ip.Split(',')[0].Trim();
        }
    
        if (string.IsNullOrEmpty(ip) || "unknown".Equals(ip, StringComparison.OrdinalIgnoreCase))
        {
            ip = headers.Get(AsciiString.Cached("Proxy-Client-IP"),null)?.ToString();
        }
    
        if (string.IsNullOrEmpty(ip) || "unknown".Equals(ip, StringComparison.OrdinalIgnoreCase))
        {
            ip = headers.Get(AsciiString.Cached("WL-Proxy-Client-IP"),null)?.ToString();
        }
    
        if (string.IsNullOrEmpty(ip) || "unknown".Equals(ip, StringComparison.OrdinalIgnoreCase))
        {
            ip = headers.Get(AsciiString.Cached("X-Real-IP"),null)?.ToString();
        }
    
        return ip;
    }

    public static String GetIP(IChannelHandlerContext ctx)
    {
        Object ip = ctx.GetAttribute(IPKey).Get();
        if (ip == null)
        {
            //从Channel获取IP
            return ctx.Channel.RemoteAddress.ToString().Split(':')[0];
        }
        else
        {
            return (String)ip;
        }
    }
    
    public static void SetPort(IChannelHandlerContext ctx, int port)
    {
        ctx.GetAttribute(PortKey).Set(port);
    }

    public static int GetPort(IChannelHandlerContext ctx)
    {
        Object port = ctx.GetAttribute(PortKey).Get();
        if (port == null)
        {
            //从Channel获取Port
            return int.Parse(ctx.Channel.RemoteAddress.ToString().Split(':')[1]);
        }
        else
        {
            return (int)port;
        }
    }
}