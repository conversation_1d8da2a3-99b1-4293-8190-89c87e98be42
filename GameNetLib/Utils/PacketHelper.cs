using GameNetLib.Proto;
using Google.Protobuf;

namespace GameNetLib.Utils;

/// <summary>
/// 封包工具类
/// </summary>
public static class PacketHelper
{
    /// <summary>
    /// 创建心跳包
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>心跳包</returns>
    public static PbPacket CreateHeartbeatPacket(string clientId = "")
    {
        var heartbeat = new HeartbeatPacket
        {
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
            ClientId = clientId ?? string.Empty
        };

        return new PbPacket
        {
            Cmd = PacketCommands.HEARTBEAT,
            Payload = heartbeat.ToByteString()
        };
    }

    /// <summary>
    /// 创建认证包
    /// </summary>
    /// <param name="token">认证令牌</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="version">协议版本</param>
    /// <returns>认证包</returns>
    public static PbPacket CreateAuthPacket(string token, string clientId, int version = 1)
    {
        var auth = new AuthPacket
        {
            Token = token ?? string.Empty,
            ClientId = clientId ?? string.Empty,
            Version = version
        };

        return new PbPacket
        {
            Cmd = PacketCommands.AUTH,
            Payload = auth.ToByteString()
        };
    }

    /// <summary>
    /// 创建认证响应包
    /// </summary>
    /// <param name="success">是否成功</param>
    /// <param name="message">响应消息</param>
    /// <param name="sessionId">会话ID</param>
    /// <returns>认证响应包</returns>
    public static PbPacket CreateAuthResponsePacket(bool success, string message = "", string sessionId = "")
    {
        var authResponse = new AuthResponsePacket
        {
            Success = success,
            Message = message ?? string.Empty,
            SessionId = sessionId ?? string.Empty
        };

        return new PbPacket
        {
            Cmd = PacketCommands.AUTH_RESPONSE,
            Payload = authResponse.ToByteString()
        };
    }

    /// <summary>
    /// 创建错误包
    /// </summary>
    /// <param name="errorCode">错误码</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>错误包</returns>
    public static PbPacket CreateErrorPacket(int errorCode, string errorMessage)
    {
        var error = new ErrorPacket
        {
            ErrorCode = errorCode,
            ErrorMessage = errorMessage ?? string.Empty
        };

        return new PbPacket
        {
            Cmd = PacketCommands.ERROR,
            Payload = error.ToByteString()
        };
    }

    /// <summary>
    /// 创建响应包
    /// </summary>
    /// <param name="requestId">请求ID</param>
    /// <param name="success">是否成功</param>
    /// <param name="data">响应数据</param>
    /// <param name="message">响应消息</param>
    /// <returns>响应包</returns>
    public static PbPacket CreateResponsePacket(int requestId, bool success, byte[]? data = null, string message = "")
    {
        var response = new ResponsePacket
        {
            RequestId = requestId,
            Success = success,
            Data = ByteString.CopyFrom(data ?? Array.Empty<byte>()),
            Message = message ?? string.Empty
        };

        return new PbPacket
        {
            Cmd = PacketCommands.RESPONSE,
            Payload = response.ToByteString()
        };
    }

    /// <summary>
    /// 创建自定义包
    /// </summary>
    /// <param name="cmd">命令ID</param>
    /// <param name="payload">载荷数据</param>
    /// <returns>自定义包</returns>
    public static PbPacket CreateCustomPacket(int cmd, byte[] payload)
    {
        return new PbPacket
        {
            Cmd = cmd,
            Payload = ByteString.CopyFrom(payload ?? Array.Empty<byte>())
        };
    }

    /// <summary>
    /// 创建自定义包
    /// </summary>
    /// <param name="cmd">命令ID</param>
    /// <param name="message">Protobuf消息</param>
    /// <returns>自定义包</returns>
    public static PbPacket CreateCustomPacket(int cmd, IMessage message)
    {
        var payload = message?.ToByteArray() ?? Array.Empty<byte>();
        return CreateCustomPacket(cmd, payload);
    }

    /// <summary>
    /// 解析心跳包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>心跳包数据</returns>
    public static HeartbeatPacket? ParseHeartbeatPacket(PbPacket packet)
    {
        if (packet?.Cmd != PacketCommands.HEARTBEAT)
            return null;

        try
        {
            return HeartbeatPacket.Parser.ParseFrom(packet.Payload);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 解析认证包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>认证包数据</returns>
    public static AuthPacket? ParseAuthPacket(PbPacket packet)
    {
        if (packet?.Cmd != PacketCommands.AUTH)
            return null;

        try
        {
            return AuthPacket.Parser.ParseFrom(packet.Payload);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 解析认证响应包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>认证响应包数据</returns>
    public static AuthResponsePacket? ParseAuthResponsePacket(PbPacket packet)
    {
        if (packet?.Cmd != PacketCommands.AUTH_RESPONSE)
            return null;

        try
        {
            return AuthResponsePacket.Parser.ParseFrom(packet.Payload);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 解析错误包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>错误包数据</returns>
    public static ErrorPacket? ParseErrorPacket(PbPacket packet)
    {
        if (packet?.Cmd != PacketCommands.ERROR)
            return null;

        try
        {
            return ErrorPacket.Parser.ParseFrom(packet.Payload);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 解析响应包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>响应包数据</returns>
    public static ResponsePacket? ParseResponsePacket(PbPacket packet)
    {
        if (packet?.Cmd != PacketCommands.RESPONSE)
            return null;

        try
        {
            return ResponsePacket.Parser.ParseFrom(packet.Payload);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 验证包格式
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>是否有效</returns>
    public static bool IsValidPacket(PbPacket? packet)
    {
        if (packet == null)
            return false;

        if (packet.Cmd <= 0)
            return false;

        if (packet.Payload == null)
            return false;

        return true;
    }

    /// <summary>
    /// 获取包的描述信息
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>描述信息</returns>
    public static string GetPacketDescription(PbPacket? packet)
    {
        if (packet == null)
            return "null packet";

        var cmdName = GetCommandName(packet.Cmd);
        var payloadSize = packet.Payload?.Length ?? 0;
        
        return $"Cmd: {packet.Cmd}({cmdName}), PayloadSize: {payloadSize}";
    }

    /// <summary>
    /// 获取命令名称
    /// </summary>
    /// <param name="cmd">命令ID</param>
    /// <returns>命令名称</returns>
    public static string GetCommandName(int cmd)
    {
        return cmd switch
        {
            PacketCommands.HEARTBEAT => "HEARTBEAT",
            PacketCommands.AUTH => "AUTH",
            PacketCommands.AUTH_RESPONSE => "AUTH_RESPONSE",
            PacketCommands.ERROR => "ERROR",
            PacketCommands.RESPONSE => "RESPONSE",
            _ => "UNKNOWN"
        };
    }

    /// <summary>
    /// 计算包的大小
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>包大小（字节）</returns>
    public static int CalculatePacketSize(PbPacket packet)
    {
        if (packet == null)
            return 0;

        return packet.CalculateSize();
    }
}

/// <summary>
/// 包命令常量
/// </summary>
public static class PacketCommands
{
    /// <summary>
    /// 心跳包
    /// </summary>
    public const int HEARTBEAT = 1;

    /// <summary>
    /// 认证包
    /// </summary>
    public const int AUTH = 2;

    /// <summary>
    /// 认证响应包
    /// </summary>
    public const int AUTH_RESPONSE = 3;

    /// <summary>
    /// 错误包
    /// </summary>
    public const int ERROR = 4;

    /// <summary>
    /// 响应包
    /// </summary>
    public const int RESPONSE = 5;

    /// <summary>
    /// 用户自定义包起始ID
    /// </summary>
    public const int USER_DEFINED_START = 1000;
}

/// <summary>
/// 错误码常量
/// </summary>
public static class ErrorCodes
{
    /// <summary>
    /// 未知错误
    /// </summary>
    public const int UNKNOWN_ERROR = 1000;

    /// <summary>
    /// 认证失败
    /// </summary>
    public const int AUTH_FAILED = 1001;

    /// <summary>
    /// 无效的包格式
    /// </summary>
    public const int INVALID_PACKET = 1002;

    /// <summary>
    /// 包太大
    /// </summary>
    public const int PACKET_TOO_LARGE = 1003;

    /// <summary>
    /// 频率限制
    /// </summary>
    public const int RATE_LIMITED = 1004;

    /// <summary>
    /// 服务器内部错误
    /// </summary>
    public const int INTERNAL_ERROR = 1005;

    /// <summary>
    /// 连接超时
    /// </summary>
    public const int CONNECTION_TIMEOUT = 1006;

    /// <summary>
    /// 不支持的协议版本
    /// </summary>
    public const int UNSUPPORTED_VERSION = 1007;
}
