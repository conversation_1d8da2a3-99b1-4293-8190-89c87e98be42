using System.Collections.Concurrent;

namespace GameNetLib.Logging;

/// <summary>
/// 默认日志器实现
/// </summary>
public class DefaultLogger : ILogger
{
    private readonly string _categoryName;
    private readonly ILoggerProvider _provider;

    /// <summary>
    /// 最小日志级别
    /// </summary>
    public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <param name="provider">日志提供者</param>
    public DefaultLogger(string categoryName, ILoggerProvider provider)
    {
        _categoryName = categoryName ?? throw new ArgumentNullException(nameof(categoryName));
        _provider = provider ?? throw new ArgumentNullException(nameof(provider));
    }

    /// <summary>
    /// 记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    public void Log(LogLevel level, string message, Exception? exception = null, params object[] args)
    {
        if (!IsEnabled(level))
            return;

        var formattedMessage = args.Length > 0 ? string.Format(message, args) : message;
        
        var entry = new LogEntry
        {
            Timestamp = DateTime.UtcNow,
            Level = level,
            CategoryName = _categoryName,
            Message = formattedMessage,
            Exception = exception,
            ThreadId = Thread.CurrentThread.ManagedThreadId
        };

        if (_provider is DefaultLoggerProvider defaultProvider)
        {
            defaultProvider.WriteLog(entry);
        }
    }

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    public void LogDebug(string message, params object[] args)
    {
        Log(LogLevel.Debug, message, null, args);
    }

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    public void LogInformation(string message, params object[] args)
    {
        Log(LogLevel.Information, message, null, args);
    }

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    public void LogWarning(string message, params object[] args)
    {
        Log(LogLevel.Warning, message, null, args);
    }

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    public void LogError(string message, Exception? exception = null, params object[] args)
    {
        Log(LogLevel.Error, message, exception, args);
    }

    /// <summary>
    /// 记录严重错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    public void LogCritical(string message, Exception? exception = null, params object[] args)
    {
        Log(LogLevel.Critical, message, exception, args);
    }

    /// <summary>
    /// 检查是否启用指定级别的日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>是否启用</returns>
    public bool IsEnabled(LogLevel level)
    {
        return level >= MinimumLevel;
    }
}

/// <summary>
/// 默认日志提供者
/// </summary>
public class DefaultLoggerProvider : ILoggerProvider
{
    private readonly ConcurrentDictionary<string, DefaultLogger> _loggers = new();
    private readonly LogLevel _minimumLevel;
    private readonly bool _writeToConsole;
    private readonly bool _writeToFile;
    private readonly string? _logFilePath;
    private readonly object _fileLock = new();
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="minimumLevel">最小日志级别</param>
    /// <param name="writeToConsole">是否写入控制台</param>
    /// <param name="writeToFile">是否写入文件</param>
    /// <param name="logFilePath">日志文件路径</param>
    public DefaultLoggerProvider(
        LogLevel minimumLevel = LogLevel.Information,
        bool writeToConsole = true,
        bool writeToFile = false,
        string? logFilePath = null)
    {
        _minimumLevel = minimumLevel;
        _writeToConsole = writeToConsole;
        _writeToFile = writeToFile;
        _logFilePath = logFilePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", "gamenetlib.log");

        // 确保日志目录存在
        if (_writeToFile && !string.IsNullOrEmpty(_logFilePath))
        {
            var directory = Path.GetDirectoryName(_logFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }
    }

    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志器</returns>
    public ILogger CreateLogger(string categoryName)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(DefaultLoggerProvider));

        return _loggers.GetOrAdd(categoryName, name =>
        {
            var logger = new DefaultLogger(name, this);
            logger.MinimumLevel = _minimumLevel;
            return logger;
        });
    }

    /// <summary>
    /// 写入日志
    /// </summary>
    /// <param name="entry">日志条目</param>
    internal void WriteLog(LogEntry entry)
    {
        if (_disposed)
            return;

        var formattedMessage = entry.Format();

        // 写入控制台
        if (_writeToConsole)
        {
            WriteToConsole(entry, formattedMessage);
        }

        // 写入文件
        if (_writeToFile && !string.IsNullOrEmpty(_logFilePath))
        {
            WriteToFile(formattedMessage);
        }
    }

    /// <summary>
    /// 写入控制台
    /// </summary>
    /// <param name="entry">日志条目</param>
    /// <param name="message">格式化消息</param>
    private static void WriteToConsole(LogEntry entry, string message)
    {
        var originalColor = Console.ForegroundColor;
        
        try
        {
            // 根据日志级别设置颜色
            Console.ForegroundColor = entry.Level switch
            {
                LogLevel.Debug => ConsoleColor.Gray,
                LogLevel.Information => ConsoleColor.White,
                LogLevel.Warning => ConsoleColor.Yellow,
                LogLevel.Error => ConsoleColor.Red,
                LogLevel.Critical => ConsoleColor.Magenta,
                _ => ConsoleColor.White
            };

            Console.WriteLine(message);
        }
        finally
        {
            Console.ForegroundColor = originalColor;
        }
    }

    /// <summary>
    /// 写入文件
    /// </summary>
    /// <param name="message">消息</param>
    private void WriteToFile(string message)
    {
        try
        {
            lock (_fileLock)
            {
                File.AppendAllText(_logFilePath!, message + Environment.NewLine);
            }
        }
        catch
        {
            // 忽略文件写入异常
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _loggers.Clear();
    }
}

/// <summary>
/// 默认日志工厂
/// </summary>
public class DefaultLoggerFactory : ILoggerFactory
{
    private readonly List<ILoggerProvider> _providers = new();
    private readonly ConcurrentDictionary<string, ILogger> _loggers = new();
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="minimumLevel">最小日志级别</param>
    /// <param name="writeToConsole">是否写入控制台</param>
    /// <param name="writeToFile">是否写入文件</param>
    /// <param name="logFilePath">日志文件路径</param>
    public DefaultLoggerFactory(
        LogLevel minimumLevel = LogLevel.Information,
        bool writeToConsole = true,
        bool writeToFile = false,
        string? logFilePath = null)
    {
        var defaultProvider = new DefaultLoggerProvider(minimumLevel, writeToConsole, writeToFile, logFilePath);
        AddProvider(defaultProvider);
    }

    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志器</returns>
    public ILogger CreateLogger(string categoryName)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(DefaultLoggerFactory));

        return _loggers.GetOrAdd(categoryName, name =>
        {
            // 使用第一个提供者创建日志器
            var provider = _providers.FirstOrDefault();
            return provider?.CreateLogger(name) ?? new NullLogger();
        });
    }

    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <returns>日志器</returns>
    public ILogger CreateLogger<T>()
    {
        return CreateLogger(typeof(T).FullName ?? typeof(T).Name);
    }

    /// <summary>
    /// 添加日志提供者
    /// </summary>
    /// <param name="provider">日志提供者</param>
    public void AddProvider(ILoggerProvider provider)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(DefaultLoggerFactory));

        if (provider == null)
            throw new ArgumentNullException(nameof(provider));

        _providers.Add(provider);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        foreach (var provider in _providers)
        {
            provider.Dispose();
        }

        _providers.Clear();
        _loggers.Clear();
    }
}

/// <summary>
/// 空日志器（不输出任何日志）
/// </summary>
public class NullLogger : ILogger
{
    public LogLevel MinimumLevel { get; set; } = LogLevel.None;

    public void Log(LogLevel level, string message, Exception? exception = null, params object[] args) { }
    public void LogDebug(string message, params object[] args) { }
    public void LogInformation(string message, params object[] args) { }
    public void LogWarning(string message, params object[] args) { }
    public void LogError(string message, Exception? exception = null, params object[] args) { }
    public void LogCritical(string message, Exception? exception = null, params object[] args) { }
    public bool IsEnabled(LogLevel level) => false;
}
