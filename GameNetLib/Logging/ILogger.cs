namespace GameNetLib.Logging;

/// <summary>
/// 日志级别
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 调试
    /// </summary>
    Debug = 0,

    /// <summary>
    /// 信息
    /// </summary>
    Information = 1,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 2,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 3,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical = 4,

    /// <summary>
    /// 无日志
    /// </summary>
    None = 5
}

/// <summary>
/// 日志接口
/// </summary>
public interface ILogger
{
    /// <summary>
    /// 最小日志级别
    /// </summary>
    LogLevel MinimumLevel { get; set; }

    /// <summary>
    /// 记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    void Log(LogLevel level, string message, Exception? exception = null, params object[] args);

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    void LogError(string message, Exception? exception = null, params object[] args);

    /// <summary>
    /// 记录严重错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="args">格式化参数</param>
    void LogCritical(string message, Exception? exception = null, params object[] args);

    /// <summary>
    /// 检查是否启用指定级别的日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>是否启用</returns>
    bool IsEnabled(LogLevel level);
}

/// <summary>
/// 日志工厂接口
/// </summary>
public interface ILoggerFactory : IDisposable
{
    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志器</returns>
    ILogger CreateLogger(string categoryName);

    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <returns>日志器</returns>
    ILogger CreateLogger<T>();

    /// <summary>
    /// 添加日志提供者
    /// </summary>
    /// <param name="provider">日志提供者</param>
    void AddProvider(ILoggerProvider provider);
}

/// <summary>
/// 日志提供者接口
/// </summary>
public interface ILoggerProvider : IDisposable
{
    /// <summary>
    /// 创建日志器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <returns>日志器</returns>
    ILogger CreateLogger(string categoryName);
}

/// <summary>
/// 日志条目
/// </summary>
public class LogEntry
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; }

    /// <summary>
    /// 类别名称
    /// </summary>
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 线程ID
    /// </summary>
    public int ThreadId { get; set; }

    /// <summary>
    /// 格式化日志条目
    /// </summary>
    /// <returns>格式化后的字符串</returns>
    public string Format()
    {
        var levelStr = Level.ToString().ToUpper().PadRight(11);
        var timestampStr = Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
        var threadStr = ThreadId.ToString().PadLeft(3);
        
        var message = $"[{timestampStr}] [{levelStr}] [{threadStr}] {CategoryName}: {Message}";
        
        if (Exception != null)
        {
            message += Environment.NewLine + Exception.ToString();
        }
        
        return message;
    }
}

/// <summary>
/// 日志扩展方法
/// </summary>
public static class LoggerExtensions
{
    /// <summary>
    /// 记录调试日志（带异常）
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    public static void LogDebug(this ILogger logger, Exception exception, string message, params object[] args)
    {
        logger.Log(LogLevel.Debug, message, exception, args);
    }

    /// <summary>
    /// 记录信息日志（带异常）
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    public static void LogInformation(this ILogger logger, Exception exception, string message, params object[] args)
    {
        logger.Log(LogLevel.Information, message, exception, args);
    }

    /// <summary>
    /// 记录警告日志（带异常）
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="exception">异常</param>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    public static void LogWarning(this ILogger logger, Exception exception, string message, params object[] args)
    {
        logger.Log(LogLevel.Warning, message, exception, args);
    }

    /// <summary>
    /// 开始日志作用域
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="state">状态对象</param>
    /// <returns>作用域</returns>
    public static IDisposable BeginScope(this ILogger logger, object state)
    {
        return new LogScope(state);
    }

    /// <summary>
    /// 开始日志作用域
    /// </summary>
    /// <param name="logger">日志器</param>
    /// <param name="messageFormat">消息格式</param>
    /// <param name="args">参数</param>
    /// <returns>作用域</returns>
    public static IDisposable BeginScope(this ILogger logger, string messageFormat, params object[] args)
    {
        return new LogScope(string.Format(messageFormat, args));
    }
}

/// <summary>
/// 日志作用域
/// </summary>
internal class LogScope : IDisposable
{
    private readonly object _state;
    private bool _disposed = false;

    public LogScope(object state)
    {
        _state = state;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
        }
    }

    public override string ToString()
    {
        return _state?.ToString() ?? string.Empty;
    }
}
