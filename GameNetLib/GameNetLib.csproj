<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>GameNetLib</AssemblyTitle>
    <AssemblyDescription>High-performance, low-latency, cross-platform C# network library for game servers</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Authors>GameNetLib Team</Authors>
    <Company>GameNetLib</Company>
    <Product>GameNetLib</Product>
    <Copyright>Copyright © 2025</Copyright>
    <PackageProjectUrl>https://github.com/gamenetlib/gamenetlib</PackageProjectUrl>
    <RepositoryUrl>https://github.com/gamenetlib/gamenetlib</RepositoryUrl>
    <PackageTags>game;network;server;tcp;websocket;kcp;protobuf;dotnetty</PackageTags>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DotNetty.Transport" Version="0.7.5" />
    <PackageReference Include="DotNetty.Codecs.Http" Version="0.7.5" />
    <PackageReference Include="Google.Protobuf" Version="3.25.1" />
    <PackageReference Include="Google.Protobuf.Tools" Version="3.25.1" />
    <PackageReference Include="Grpc.Tools" Version="2.59.0" PrivateAssets="All" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="KCP" Version="1.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Proto\**\*.proto" GrpcServices="None" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Configuration\" />
    <Folder Include="tcp\" />
  </ItemGroup>

</Project>
