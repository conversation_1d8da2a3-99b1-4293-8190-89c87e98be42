using System.Net.WebSockets;
using DotNetty.Buffers;
using GameNetLib.Core.Interfaces;
using GameNetLib.Proto;
using GameNetLib.Utils;
using Google.Protobuf;

namespace GameNetLib.Core.WebSocket;

public class WSClientChannel<T> : INetChannel<T>
{
    private readonly ClientWebSocket _webSocket;
    private readonly string _remoteAddress;
    private bool _isReading = false;

    public WSClientChannel(ClientWebSocket webSocket, string remoteAddress)
    {
        _webSocket = webSocket ?? throw new ArgumentNullException(nameof(webSocket));
        _remoteAddress = remoteAddress ?? "Unknown";
    }

    public string RemoteAddress()
    {
        return _remoteAddress;
    }

    public int RemotePort()
    {
        return 0;
    }

    public Task SendAsync(T packet)
    {
        if (packet == null)
        {
            return Task.FromException(new ArgumentNullException(nameof(packet)));
        }

        if (_webSocket?.State != WebSocketState.Open)
        {
            return Task.FromException(new InvalidOperationException("WebSocket is not open"));
        }

        try
        {
            if (typeof(T) == typeof(Packet))
            {
                //create a byte buffer
                IByteBuffer buffer = Unpooled.Buffer();
                Packet p = packet as Packet;
                if (p == null)
                {
                    return Task.FromException(new InvalidCastException("Failed to cast packet to Packet type"));
                }

                //write ptCode big-endian
                ByteOrderHelper.WriteInt(buffer, p._PTCode, ByteOrderHelper.ByteOrder.BigEndian);
                //write sn  big-endian
                ByteOrderHelper.WriteLong(buffer, p._sn, ByteOrderHelper.ByteOrder.BigEndian);
                //write data
                if (p._Data != null)
                {
                    buffer.WriteBytes(p._Data);
                }
                
                byte[] bytes = new byte[buffer.ReadableBytes];
                buffer.GetBytes(0, bytes);

                return _webSocket.SendAsync(new ArraySegment<byte>(bytes), WebSocketMessageType.Binary, true,
                    CancellationToken.None);
            }
            else if (typeof(T) == typeof(IMessage))
            {
                IMessage p = packet as IMessage;
                if (p == null)
                {
                    return Task.FromException(new InvalidCastException("Failed to cast packet to IMessage type"));
                }

                return _webSocket.SendAsync(new ArraySegment<byte>(p.ToByteArray()), WebSocketMessageType.Binary, true,
                    CancellationToken.None);
            }
            else
            {
                return Task.FromException(new ArgumentException("Packet type not supported"));
            }
        }
        catch (Exception ex)
        {
            return Task.FromException(ex);
        }
    }

    public Task CloseAsync()
    {
        try
        {
            if (_webSocket?.State == WebSocketState.Open)
            {
                return _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, string.Empty, CancellationToken.None);
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            return Task.FromException(ex);
        }
    }

    public async void ReadMessageAsync(INetConnection<T> connection)
    {
        if (_isReading || connection == null)
        {
            return;
        }

        if (_webSocket?.State != WebSocketState.Open)
        {
            return;
        }

        _isReading = true;

        try
        {
            var buffer = new byte[1024*1024*3];
            var result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            if (result.MessageType == WebSocketMessageType.Close)
            {
                return;
            }

            if (result.MessageType == WebSocketMessageType.Binary && result.Count > 0)
            {
                if (typeof(T) == typeof(Packet))
                {
                    //byte[] -> IByteBuffer
                    var actualData = new byte[result.Count];
                    Array.Copy(buffer, actualData, result.Count);
                    IByteBuffer byteBuffer = Unpooled.WrappedBuffer(actualData);

                    if (byteBuffer.ReadableBytes >= 12) // 至少需要 4 + 8 字节
                    {
                        //按照big endian读取ptCode、sn、data
                        var ptCode = byteBuffer.ReadInt(ByteOrderHelper.ByteOrder.BigEndian);
                        var sn = byteBuffer.ReadLong(ByteOrderHelper.ByteOrder.BigEndian);
                        byte[] data = null;
                        if (byteBuffer.ReadableBytes > 0)
                        {
                            data = new byte[byteBuffer.ReadableBytes];
                            byteBuffer.ReadBytes(data);
                        }

                        //create a packet
                        var packet = new Packet(ptCode, sn ,data);
                        //handle packet
                        connection.AddPacket((T)(object)packet);
                    }
                }
                else if (typeof(T) == typeof(IMessage))
                {
                    //byte[] -> IMessage
                    var messageInstance = Activator.CreateInstance(typeof(T));
                    if (messageInstance is IMessage message)
                    {
                        var actualData = new byte[result.Count];
                        Array.Copy(buffer, actualData, result.Count);
                        message.MergeFrom(actualData);
                        //handle message
                        connection.AddPacket((T)message);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ReadMessageAsync: {ex.Message}");
        }
        finally
        {
            _isReading = false;
        }
    }
}