using System.Net.WebSockets;
using GameNetLib.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Core.WebSocket;

/// <summary>
/// WebSocket 客户端实现，直接使用现成库，不再使用dotnetty实现
/// </summary>
public class WSClient<T> : INetClient
{
    private IConnectionManager<T>? _connectionManager;
    private volatile bool _running;

    public WSClient()
    {
        _running = true;
    }
    
    public void Dispose()
    {
        _running = false;
    }

    public async void connectTo(string uriAdd, IConnectionManager<T> connectionManager, int id = 0)
    {
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));

        var client = new ClientWebSocket();
        var uri = new Uri(uriAdd);
        try
        {
            Console.WriteLine("Connecting..." + id);
            await client.ConnectAsync(uri, CancellationToken.None);
            Console.WriteLine("✅ Connected!" + id);

            WSClientChannel<T> channel = new WSClientChannel<T>(client, uri.Host);
            _connectionManager.AddConnection(channel);

            // 启动读取循环
            readTick();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error: " + ex.Message);
        }
    }

    public void readTick()
    {
        if (_connectionManager == null)
        {
            Console.WriteLine("Warning: Connection manager is null, cannot start read tick");
            return;
        }

        Task.Run(async () =>
        {
            while (_running && _connectionManager != null)
            {
                try
                {
                    Thread.Sleep(10);
                    foreach (var connection in _connectionManager.GetAllConnections())
                    {
                        if (connection?.IsActive() == true)
                        {
                            if (connection.Channel() is WSClientChannel<T> channel)
                            {
                                channel.ReadMessageAsync(connection);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in readTick: {ex.Message}");
                }
            }
        });
    }
}
