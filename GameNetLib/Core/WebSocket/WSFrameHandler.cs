using DotNetty.Codecs.Http;
using DotNetty.Transport.Channels;
using GameNetLib.Utils;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Core.WebSocket;

public class WSFrameHandler : SimpleChannelInboundHandler<IFullHttpRequest>
{
    
    private readonly ILogger<WSFrameHandler> _logger;
    
    public WSFrameHandler(ILogger<WSFrameHandler> logger)
    {
        _logger = logger;
    }
    
    protected override void ChannelRead0(IChannelHandlerContext ctx, IFullHttpRequest frame)
    {
        Console.WriteLine("Received frame: " + frame.GetType());
        
        // 处理不同类型的 WebSocket 帧
        switch (frame)
        {
            case IFullHttpRequest request:
                HandleHttpRequest(ctx, request);
                break;
            
            default:
                _logger.LogWarning("Unsupported WebSocket frame type: {FrameType}", frame.GetType().Name);
                break;
        }
        
        // 将请求传递到下一个 Handler（通常是 WebSocketServerProtocolHandler）
        ctx.FireChannelRead(frame.Retain());
    }
    
    private void HandleHttpRequest(IChannelHandlerContext ctx, IFullHttpRequest req)
    {
        try
        {
            // 处理 HTTP 请求
            _logger.LogDebug("Received HTTP request: {Method} {Uri}", req.Method, req.Uri);
            
            NetHelper.InitIPInfo(ctx, req);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling HTTP request");
        }
    }

}