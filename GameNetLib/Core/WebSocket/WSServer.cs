using DotNetty.Codecs.Http;
using DotNetty.Codecs.Http.WebSockets;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using GameNetLib.Codec.WebSocket;
using GameNetLib.Core.Interfaces;
using GameNetLib.Core.Tcp;
using GameNetLib.Proto;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Core.WebSocket;

/// <summary>
/// WebSocket 服务器实现
/// </summary>
public class WSServer<T> : INetServer<T>
{
    public IConnectionManager<T> ConnectionManager { get;}
    private readonly ILogger<WSServer<T>> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IConnectionManager<T> _connectionManager;

    private IEventLoopGroup? _bossGroup;
    private IEventLoopGroup? _workerGroup;
    private IChannel? _serverChannel;

    private readonly int _port;
    private readonly string _websocketPath;
    private readonly IMessage? _pbPacket;

    public WSServer(IConnectionManager<T> connectionManager, int port, string websocketPath = "/websocket", IMessage? pbPacket = null, ILoggerFactory? loggerFactory = null)
    {
        _port = port;
        _websocketPath = websocketPath;
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        ConnectionManager = _connectionManager;
        _loggerFactory = loggerFactory ?? LoggerFactory.Create(builder =>
        {
            builder.AddConsole()
                .SetMinimumLevel(LogLevel.Information);
        });
        _logger = _loggerFactory.CreateLogger<WSServer<T>>();
        _pbPacket = pbPacket;
    }

    public static WSServer<Packet> Start(IConnectionManager<Packet> connectionManager, int port, string websocketPath = "/ws", ILoggerFactory? loggerFactory = null)
    {
        var server = new WSServer<Packet>(connectionManager, port, websocketPath, null, loggerFactory);
        server.StartAsync().Wait();
        return server;
    }

    public static WSServer<IMessage> Start(IConnectionManager<IMessage> connectionManager, int port, IMessage pbPacket, string websocketPath = "/ws", ILoggerFactory? loggerFactory = null)
    {
        var server = new WSServer<IMessage>(connectionManager, port, websocketPath, pbPacket, loggerFactory);
        server.StartAsync().Wait();
        return server;
    }

    public void Start()
    {
        StartAsync().Wait();
    }

    public void Stop()
    {
        StopAsync().Wait();
    }

    public async Task StartAsync()
    {
        try
        {
            _bossGroup = new MultithreadEventLoopGroup(1);
            _workerGroup = new MultithreadEventLoopGroup(Environment.ProcessorCount);

            var bootstrap = new ServerBootstrap()
                .Group(_bossGroup, _workerGroup)
                .Channel<TcpServerSocketChannel>()
                .Option(ChannelOption.SoBacklog, 2000)
                .Option(ChannelOption.SoReuseaddr, true)
                .ChildOption(ChannelOption.TcpNodelay, true)
                .ChildOption(ChannelOption.SoKeepalive, true)
                .ChildOption(ChannelOption.SoRcvbuf, 1024 * 1024)
                .ChildOption(ChannelOption.SoSndbuf, 1024 * 1024)
                .ChildHandler(new ActionChannelInitializer<ISocketChannel>(channel =>
                {
                    var pipeline = channel.Pipeline;

                    // HTTP 编解码器
                    pipeline.AddLast("httpCodec", new HttpServerCodec());
                    pipeline.AddLast("httpAggregator", new HttpObjectAggregator(65536));
                    
                    //WS处理器
                    // pipeline.AddLast("wsHandler", new WSFrameHandler(_loggerFactory.CreateLogger<WSFrameHandler>()));

                    // WebSocket 协议处理器
                    pipeline.AddLast("websocketHandler", new WebSocketServerProtocolHandler(_websocketPath, null, true));
                    
                    
                    
                    // pipeline.AddLast("wsDecoder", new WSDecoder(_loggerFactory.CreateLogger<WSDecoder>(), _pbPacket));
                    // pipeline.AddLast("wsEncoder", new WSEncoder<T>(_loggerFactory.CreateLogger<WSEncoder<T>>()));
                    
                    
                    //通用TCP处理器
                    // pipeline.AddLast("handler", new TcpHandler<T>(_connectionManager, _loggerFactory.CreateLogger<TcpHandler<T>>()));
                    
                }));

            _serverChannel = await bootstrap.BindAsync(_port);

            _logger.LogInformation("WebSocket server started on :{Port} with path {Path}", _port, _websocketPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start WebSocket server");
            throw;
        }
    }

    public async Task StopAsync()
    {
        try
        {
            if (_serverChannel != null)
            {
                await _serverChannel.CloseAsync();
            }

            await Task.WhenAll(
                _bossGroup?.ShutdownGracefullyAsync() ?? Task.CompletedTask,
                _workerGroup?.ShutdownGracefullyAsync() ?? Task.CompletedTask
            );

            _logger.LogInformation("WebSocket server stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping WebSocket server");
            throw;
        }
    }

    public void Dispose()
    {
        try
        {
            StopAsync().Wait(TimeSpan.FromSeconds(10));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing WebSocket server");
        }
    }
}