using GameNetLib.Core.Interfaces;
using Google.Protobuf;

namespace GameNetLib.Core;

public abstract class AbstractNetConnection<T> : INetConnection<T>
{
    private static long _idCounter = 10001;
    private bool connected = true;
    
    protected AbstractNetConnection(INetChannel<T>channel)
    {
        Channel = channel;
        LastReceiveTime = DateTime.UtcNow;
        //生成自增id
        Id = Interlocked.Increment(ref _idCounter); 
    }

    public void Dispose()
    {
        Channel.CloseAsync();
    }

    public long Id { get; }
    public INetChannel<T> Channel { get; }
    public DateTime LastReceiveTime { get; set; }

    long INetConnection<T>.Id()
    {
        return Id;
    }

    INetChannel<T> INetConnection<T>.Channel()
    {
        return Channel;
    }

    public void AddPacket(T packet)
    {
        LastReceiveTime = DateTime.UtcNow;
        SubAddPacket(packet);
    }

    bool INetConnection<T>.IsActive()
    {
        //最近一次收包时间超过4分钟，认为连接失效
        bool rst = (DateTime.UtcNow - LastReceiveTime).TotalSeconds < 240;
        return connected && rst ;
    }

    //生产环境避免在该方法直接处理逻辑，从而导致IO线程阻塞
    public abstract void SubAddPacket(T packet);

    public void Disconnect()
    {
        connected = false;
    }
}