using DotNetty.Transport.Channels;
using GameNetLib.Core.Interfaces;
using GameNetLib.Utils;
using Google.Protobuf;

namespace GameNetLib.Core.Tcp;

/// <summary>
/// TCP 通道实现（泛型版本）
/// </summary>
public class TcpChannel<T> : INetChannel<T>
{
    private IChannelHandlerContext ctx;

    public string RemoteAddress()
    {
        return NetHelper.GetIP(ctx);
    }

    public int RemotePort()
    {
        return NetHelper.GetPort(ctx);
    }

    public TcpChannel(IChannelHandlerContext ctx)
    {
        this.ctx = ctx;
    }

    public Task SendAsync(T packet)
    {
        return ctx.WriteAndFlushAsync(packet);
    }

    public Task CloseAsync()
    {
        return ctx.CloseAsync();
    }
}