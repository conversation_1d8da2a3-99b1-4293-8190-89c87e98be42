using DotNetty.Common.Utilities;
using DotNetty.Transport.Channels;
using GameNetLib.Core.Interfaces;
using GameNetLib.Utils;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Core.Tcp;

public class TcpHandler<T> : SimpleChannelInboundHandler<T>
{
    private readonly ILogger<TcpHandler<T>> _logger;
    private readonly IConnectionManager<T> _connectionManager;
    
    public TcpHandler(IConnectionManager<T> connectionManager, ILogger<TcpHandler<T>> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
    }
    
    protected override void ChannelRead0(IChannelHandlerContext ctx, T msg)
    {
        try
        {
            if (ctx == null)
            {
                _logger.LogError("ChannelHandlerContext is null in ChannelRead0");
                return;
            }

            if (msg == null)
            {
                _logger.LogWarning("Received null message in ChannelRead0");
                return;
            }

            // 从上下文中获取连接ID
            var connectionIdObj = ctx.GetAttribute(NetConstants.ConnectionIdKey)?.Get();
            if (connectionIdObj is long connectionId)
            {
                var connection = _connectionManager.GetConnection(connectionId);
                if (connection != null)
                {
                    connection.AddPacket(msg);
                }
                else
                {
                    _logger.LogWarning("Connection not found for ID: {ConnectionId}", connectionId);
                }
            }
            else
            {
                var remoteAddress = ctx.Channel?.RemoteAddress?.ToString() ?? "Unknown";
                _logger.LogWarning("No connection ID found in channel context for remote: {RemoteAddress}", remoteAddress);
            }
        }
        catch (Exception ex)
        {
            var remoteAddress = ctx?.Channel?.RemoteAddress?.ToString() ?? "Unknown";
            _logger.LogError(ex, "Error processing received message from {RemoteAddress}", remoteAddress);
        }
    }

    public override void ChannelActive(IChannelHandlerContext context)
    {
        try
        {
            if (context == null)
            {
                _logger.LogError("ChannelHandlerContext is null in ChannelActive");
                return;
            }

            if (context.Channel == null)
            {
                _logger.LogError("Channel is null in ChannelActive");
                context.CloseAsync();
                return;
            }

            // 创建 TCP 连接
            var tcpChannel = new TcpChannel<T>(context);

            // 添加到连接管理器并获取连接ID
            long connectionId = _connectionManager.AddConnection(tcpChannel);

            // 将连接ID存储到通道属性中
            context.GetAttribute(NetConstants.ConnectionIdKey).Set(connectionId);

            var remoteAddress = context.Channel.RemoteAddress?.ToString() ?? "Unknown";
            _logger.LogInformation("Client connected with ID: {ConnectionId}, Remote: {RemoteAddress}",
                connectionId, remoteAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client connection");
            try
            {
                context?.CloseAsync();
            }
            catch (Exception closeEx)
            {
                _logger.LogError(closeEx, "Error closing context after connection error");
            }
        }
    }

    public override void ChannelInactive(IChannelHandlerContext context)
    {
        try
        {
            // 从上下文中获取连接ID
            var connectionIdObj = context.GetAttribute(NetConstants.ConnectionIdKey).Get();
            if (connectionIdObj is long connectionId)
            {
                var connection = _connectionManager.GetConnection(connectionId);
                if (connection != null)
                {
                    //关闭连接
                    connection.Disconnect();
                    _logger.LogInformation("Client disconnected with ID: {ConnectionId}, Remote: {RemoteAddress}",
                        connectionId, context.Channel.RemoteAddress);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client disconnection");
        }
        finally
        {
            base.ChannelInactive(context);
        }
    }

    public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
    {
        var remoteAddress = context?.Channel?.RemoteAddress?.ToString() ?? "Unknown";

        // 根据异常类型决定日志级别
        if (exception is System.Net.Sockets.SocketException socketEx)
        {
            // 对于常见的网络断开异常，使用较低的日志级别
            if (socketEx.ErrorCode == 10053 || socketEx.ErrorCode == 10054) // 连接被重置或中止
            {
                _logger.LogDebug("Client disconnected unexpectedly: {RemoteAddress}, Error: {ErrorCode}",
                    remoteAddress, socketEx.ErrorCode);
            }
            else
            {
                _logger.LogWarning("Socket exception for {RemoteAddress}: {Message} (ErrorCode: {ErrorCode})",
                    remoteAddress, socketEx.Message, socketEx.ErrorCode);
            }
        }
        else
        {
            _logger.LogError(exception, "Exception caught in channel handler for {RemoteAddress}", remoteAddress);
        }

        try
        {
            if (context != null)
            {
                // 从上下文中获取连接ID
                var connectionIdObj = context.GetAttribute(NetConstants.ConnectionIdKey)?.Get();
                if (connectionIdObj is long connectionId)
                {
                    var connection = _connectionManager.GetConnection(connectionId);
                    if (connection != null)
                    {
                        //关闭连接
                        connection.Disconnect();
                        _logger.LogDebug("Connection {ConnectionId} marked as disconnected due to exception", connectionId);
                    }
                }

                // 确保通道被关闭
                if (context.Channel?.Active == true)
                {
                    context.CloseAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing channel after exception for {RemoteAddress}", remoteAddress);
        }

        try
        {
            base.ExceptionCaught(context, exception);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling base.ExceptionCaught for {RemoteAddress}", remoteAddress);
        }
    }
}