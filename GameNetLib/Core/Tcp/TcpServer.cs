using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using GameNetLib.Core.Interfaces;
using Microsoft.Extensions.Logging;
using GameNetLib.Codec;
using GameNetLib.Proto;
using Google.Protobuf;

namespace GameNetLib.Core.Tcp;

/// <summary>
/// TCP服务器实现
/// </summary>
public class TcpServer<T> : INetServer<T>
{
    public IConnectionManager<T> ConnectionManager { get; }
    private  ILogger<TcpServer<T>> _logger;
    private  ILoggerFactory _loggerFactory;
    private  IConnectionManager<T> _connectionManager;
    
    private IEventLoopGroup? _bossGroup;
    private IEventLoopGroup? _workerGroup;
    private IChannel? _serverChannel;

    private int _port;
    private IMessage _pbPacket;

    public TcpServer(IConnectionManager<T> connectionManager,int port, IMessage pbPacket,ILoggerFactory? loggerFactory = null)
    {
        _port = port;
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _loggerFactory = loggerFactory ?? LoggerFactory.Create(builder =>
        {
            builder.AddConsole()
                .SetMinimumLevel(LogLevel.Information);
        });
        _logger = _loggerFactory.CreateLogger<TcpServer<T>>();
        _pbPacket = pbPacket;
    }
    
    public static TcpServer<Packet>  start(IConnectionManager<Packet> connectionManager, int port, ILoggerFactory? loggerFactory = null)
    {
        var server = new TcpServer<Packet>(connectionManager, port, null, loggerFactory);
        server.StartAsync().Wait();
        return server;
    }
    
    public static TcpServer<IMessage>  start(IConnectionManager<IMessage> connectionManager, int port, IMessage pbPacket, ILoggerFactory? loggerFactory = null)
    {
        var server = new TcpServer<IMessage>(connectionManager, port, pbPacket, loggerFactory);
        server.StartAsync().Wait();
        return server;
    }

    public void Start()
    {
        throw new NotImplementedException();
    }

    public void Stop()
    {
        throw new NotImplementedException();
    }

    public async Task StartAsync()
    {
        try
        {
            _bossGroup = new MultithreadEventLoopGroup(1);
            _workerGroup = new MultithreadEventLoopGroup(Environment.ProcessorCount);

            var bootstrap = new ServerBootstrap()
                .Group(_bossGroup, _workerGroup)
                .Channel<TcpServerSocketChannel>()
                .Option(ChannelOption.SoBacklog, 2000)
                .Option(ChannelOption.SoReuseaddr, true)
                .ChildOption(ChannelOption.TcpNodelay, true)
                .ChildOption(ChannelOption.SoKeepalive, true)
                .ChildOption(ChannelOption.SoRcvbuf, 1024*1024)
                .ChildOption(ChannelOption.SoSndbuf, 1024*1024)
                .ChildHandler(new ActionChannelInitializer<ISocketChannel>(channel =>
                {
                    var pipeline = channel.Pipeline;
                    if (_pbPacket != null)
                    {
                        pipeline.AddLast("frameDecoder", new GyTCPDecoder(_loggerFactory.CreateLogger<GyTCPDecoder>()));
                        pipeline.AddLast("frameEncoder", new GyTCPEncoder(_loggerFactory.CreateLogger<GyTCPEncoder>()));
                        pipeline.AddLast("protobufDecoder", new ProtobufDecoder(_loggerFactory.CreateLogger<ProtobufDecoder>(), _pbPacket));
                    }
                    else
                    {
                        pipeline.AddLast("frameDecoder", new TripleTCPDecoder(_loggerFactory.CreateLogger<TripleTCPDecoder>()));
                        pipeline.AddLast("frameEncoder", new TripleTCPEncoder(_loggerFactory.CreateLogger<TripleTCPEncoder>()));
                    }
                    
                    pipeline.AddLast("handler", new TcpHandler<T>(_connectionManager, _loggerFactory.CreateLogger<TcpHandler<T>>()));
                }));

            _serverChannel = await bootstrap.BindAsync(_port);
            
            _logger.LogInformation("TCP server started on :{Port}", _port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start TCP server");
            throw;
        }
    }

    public async Task StopAsync()
    {
        try
        {
            if (_serverChannel != null)
            {
                await _serverChannel.CloseAsync();
            }

            await Task.WhenAll(
                _bossGroup?.ShutdownGracefullyAsync() ?? Task.CompletedTask,
                _workerGroup?.ShutdownGracefullyAsync() ?? Task.CompletedTask
            );

            _logger.LogInformation("TCP server stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping TCP server");
            throw;
        }
    }

    public void Dispose()
    {
        _bossGroup.ShutdownGracefullyAsync().Wait();
        _workerGroup.ShutdownGracefullyAsync().Wait();
    }
}