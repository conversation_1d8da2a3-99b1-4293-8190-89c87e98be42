using System.ComponentModel.Design;
using System.Security.Cryptography;
using DotNetty.Buffers;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using GameNetLib.Codec;
using GameNetLib.Core.Interfaces;
using GameNetLib.Proto;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Core.Tcp;

public class TcpClient<T> : INetClient
{
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILogger _logger;
    private readonly IConnectionManager<T> _connectionManager;
    
    private IEventLoopGroup? _group;
    private Bootstrap? _bootstrap;
    private IMessage _pbPacket;
    
    public TcpClient(IConnectionManager<T> connectionManager,IMessage pbPacket, ILoggerFactory? loggerFactory = null)
    {
        _loggerFactory = loggerFactory ?? Microsoft.Extensions.Logging.LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        _logger = _loggerFactory.CreateLogger<TcpClient<T>>();

        _connectionManager = connectionManager;
        _group = new MultithreadEventLoopGroup();
        
        _pbPacket = pbPacket;

        _bootstrap = new Bootstrap()
            .Group(_group)
            .Channel<TcpSocketChannel>()
            .Option(ChannelOption.TcpNodelay, true)
            .Handler(new ActionChannelInitializer<ISocketChannel>(channel =>
            {
                var pipeline = channel.Pipeline;
                if(this is TcpClient<Packet>)
                {
                    pipeline.AddLast("frameDecoder", new TripleTCPDecoder(_loggerFactory.CreateLogger<TripleTCPDecoder>()));
                    pipeline.AddLast("frameEncoder", new TripleTCPEncoder(_loggerFactory.CreateLogger<TripleTCPEncoder>()));
                }
                else
                {
                    pipeline.AddLast("frameDecoder", new GyTCPDecoder(_loggerFactory.CreateLogger<GyTCPDecoder>()));
                    pipeline.AddLast("frameEncoder", new GyTCPEncoder(_loggerFactory.CreateLogger<GyTCPEncoder>()));
                    pipeline.AddLast("protobufDecoder", new ProtobufDecoder(_loggerFactory.CreateLogger<ProtobufDecoder>(), _pbPacket));
                }
                
                pipeline.AddLast("handler", new TcpHandler<T>(_connectionManager, _loggerFactory.CreateLogger<TcpHandler<T>>()));
            }));
    }

    public Task ConnectAsync(string host, int port)
    {
        return _bootstrap.ConnectAsync(host, port);
    }
    
    public static void connectTo(string host, int port, IConnectionManager<Packet> connectionManager)
    {
        var client = new TcpClient<Packet>(connectionManager,null);
        client.ConnectAsync(host, port).Wait();
    }
    
    public static void connectTo(string host, int port, IMessage pbPacket, IConnectionManager<IMessage> connectionManager)
    {
        var client = new TcpClient<IMessage>(connectionManager, pbPacket);
        client.ConnectAsync(host, port).Wait();
    }

    public void Dispose()
    {
        _group.ShutdownGracefullyAsync().Wait();
    }
}