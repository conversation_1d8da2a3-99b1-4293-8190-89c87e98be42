using Google.Protobuf;

namespace GameNetLib.Core.Interfaces;

/// <summary>
/// 网络通道接口（非泛型版本）
/// </summary>
// public interface INetChannel
// {
//     /// <summary>
//     /// 远程地址
//     /// </summary>
//     string RemoteAddress();
//
//     /// <summary>
//     /// 远程端口
//     /// </summary>
//     int RemotePort();
//
//     /// <summary>
//     /// 发送Protobuf包
//     /// </summary>
//     /// <param name="packet">Protobuf包</param>
//     /// <returns>发送任务</returns>
//     public Task SendAsync(IMessage packet);
//
//     /// <summary>
//     /// 关闭连接
//     /// </summary>
//     /// <returns>关闭任务</returns>
//     public Task CloseAsync();
// }

/// <summary>
/// 网络通道接口（泛型版本）
/// </summary>
public interface INetChannel<T>
{
    /// <summary>
    /// 远程地址
    /// </summary>
    string RemoteAddress();

    /// <summary>
    /// 远程端口
    /// </summary>
    int RemotePort();

    /// <summary>
    /// 发送数据包
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns>发送任务</returns>
    public Task SendAsync(T packet);

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <returns>关闭任务</returns>
    public Task CloseAsync();
}