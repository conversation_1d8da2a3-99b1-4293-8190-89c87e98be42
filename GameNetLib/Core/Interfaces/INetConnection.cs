using System.Threading.Channels;
using GameNetLib.Proto;
using Google.Protobuf;

namespace GameNetLib.Core.Interfaces;

/// <summary>
/// 网络连接接口
/// </summary>
public interface INetConnection<T> : IDisposable
{
    /// <summary>
    /// 连接唯一ID
    /// </summary>
    long Id();

    /// <summary>
    /// 连接通道
    /// </summary>
    INetChannel<T> Channel();

    /// <summary>
    /// 远程地址
    /// </summary>
    string RemoteAddress() { return Channel().RemoteAddress(); }

    /// <summary>
    /// 远程端口
    /// </summary>
    int RemotePort() { return Channel().RemotePort(); }

    /// <summary>
    /// 发送Protobuf包
    /// </summary>
    /// <param name="packet">Protobuf包</param>
    /// <returns>发送任务</returns>
    public Task SendAsync(T packet){
        return Channel().SendAsync(packet);
    }

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <returns>关闭任务</returns>
    void Disconnect();

    /// <summary>
    /// 收到Protobuf包
    /// </summary>
    /// <param name="packet">Protobuf包</param>
    void AddPacket(T packet);

    /// <summary>
    /// 是否活跃
    /// </summary>
    bool IsActive();
}
