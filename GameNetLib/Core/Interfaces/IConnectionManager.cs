namespace GameNetLib.Core.Interfaces;

/// <summary>
/// 连接管理器接口
/// </summary>
public interface IConnectionManager<T>
{
    /// <summary>
    /// 添加连接,返回连接id
    /// </summary>
    /// <param name="connection">连接</param>
    /// <returns>连接ID</returns>
    long AddConnection(INetChannel<T> channel);

    /// <summary>
    /// 获取连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>连接对象，如果不存在返回null</returns>
    INetConnection<T>? GetConnection(long connectionId);

    /// <summary>
    /// 获取所有连接
    /// </summary>
    /// <returns>所有连接的集合</returns>
    IEnumerable<INetConnection<T>> GetAllConnections();

    /// <summary>
    /// 获取连接数量
    /// </summary>
    /// <returns>连接数量</returns>
    int GetConnectionCount();
}
