using DotNetty.Codecs;
using DotNetty.Codecs.Http.WebSockets;
using DotNetty.Transport.Channels;
using GameNetLib.Proto;
using GameNetLib.Utils;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Codec.WebSocket;

public class WSEncoder<T> : MessageToMessageEncoder<T>
{
    private ILogger<WSEncoder<T>> _logger;
    public WSEncoder(ILogger<WSEncoder<T>> logger)
    {
        _logger = logger;
    }
    
    protected override void Encode(IChannelHandlerContext ctx, T message, List<object> output)
    {
        if (typeof(T) == typeof(Packet))
        {
            Packet packet = message as Packet;
            var outData = ctx.Allocator.Buffer();
            ByteOrderHelper.WriteInt(outData, packet._PTCode, ByteOrderHelper.ByteOrder.BigEndian);
            ByteOrderHelper.WriteLong(outData, packet._sn, ByteOrderHelper.ByteOrder.BigEndian);
            if(packet._Data!= null){
                outData.WriteBytes(packet._Data);
            }
            output.Add(new BinaryWebSocketFrame(outData));
        }else if (typeof(T) == typeof(IMessage))
        {
            var outData = ctx.Allocator.Buffer();
            byte[] data = ((IMessage)message).ToByteArray();
            outData.WriteBytes(data);
            output.Add(new BinaryWebSocketFrame(outData));
        }else
        {
            throw new Exception("Unsupported message type: " + typeof(T));
        }
    }
}