using System.Text;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Codecs.Http;
using DotNetty.Codecs.Http.WebSockets;
using DotNetty.Common.Utilities;
using DotNetty.Transport.Channels;
using GameNetLib.Proto;
using GameNetLib.Utils;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Codec.WebSocket;

public class WSDecoder : MessageToMessageDecoder<Object>
{
    private readonly ILogger<WSDecoder> _logger;
    private readonly IMessage? _pbPacket;
    
    public WSDecoder(ILogger<WSDecoder> logger, IMessage protoType = null)
    {
        _logger = logger;
        _pbPacket = protoType;
    }
    
    protected override void Decode(IChannelHandlerContext ctx, object frame, List<object> output)
    {
        try
        {
            // 从上下文中获取连接ID
            var connectionIdObj = ctx.GetAttribute(NetConstants.ConnectionIdKey).Get();
            if (connectionIdObj is not long connectionId)
            {
                _logger.LogWarning("No connection ID found in channel context");
                return;
            }

            // 处理不同类型的 WebSocket 帧
            switch (frame)
            {
                case IFullHttpRequest request:
                    HandleHttpRequest(ctx, request);
                    break;
                case TextWebSocketFrame textFrame:
                    HandleTextFrame(textFrame);
                    break;
                case BinaryWebSocketFrame binaryFrame:
                    HandleBinaryFrame(binaryFrame, output);
                    break;
                case PingWebSocketFrame pingFrame:
                    HandlePingFrame(ctx, pingFrame);
                    break;
                case PongWebSocketFrame pongFrame:
                    HandlePongFrame(pongFrame);
                    break;
                case CloseWebSocketFrame closeFrame:
                    HandleCloseFrame(ctx, closeFrame);
                    break;
                default:
                    _logger.LogWarning("Unsupported WebSocket frame type: {FrameType}", frame.GetType().Name);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing WebSocket frame");
        }
    }
    
    
    
    private void HandleHttpRequest(IChannelHandlerContext ctx, IFullHttpRequest req)
    {
        try
        {
            // 处理 HTTP 请求
            _logger.LogDebug("Received HTTP request: {Method} {Uri}", req.Method, req.Uri);
            
            NetHelper.InitIPInfo(ctx, req);

            // 检查请求是否成功解码以及是否为WebSocket升级请求
            if (!req.Result.IsSuccess ||
                !string.Equals("websocket", req.Headers.Get(AsciiString.Cached("Upgrade"),null)?.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                // 如果不是WebSocket升级请求，返回BAD_REQUEST响应
                SendHttpResponse(ctx, req, new DefaultFullHttpResponse(
                    HttpVersion.Http11, HttpResponseStatus.BadRequest));
                return;
            }

            // 创建WebSocket握手工厂并执行握手
            var wsFactory = new WebSocketServerHandshakerFactory(
                "", null, false, 3 * 1024 * 1024);
            var handshaker = wsFactory.NewHandshaker(req);

            if (handshaker == null)
            {
                // 不支持的WebSocket版本
                WebSocketServerHandshakerFactory.SendUnsupportedVersionResponse(ctx.Channel);
            }
            else
            {
                // 执行WebSocket握手
                handshaker.HandshakeAsync(ctx.Channel, req);
                _logger.LogDebug("WebSocket handshake completed for {RemoteAddress}", ctx.Channel.RemoteAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling HTTP request");
        }
    }

    /// <summary>
    /// 发送HTTP响应
    /// </summary>
    private static void SendHttpResponse(IChannelHandlerContext ctx, IFullHttpRequest req, IFullHttpResponse res)
    {
        // 如果响应状态不是200，记录错误
        if (res.Status.Code != 200)
        {
            var content = Unpooled.CopiedBuffer(res.Status.ToString(), Encoding.UTF8);
            res.Content.WriteBytes(content);
            content.Release();
        }

        // 发送响应并关闭连接
        var future = ctx.WriteAndFlushAsync(res);
        if (!HttpUtil.IsKeepAlive(req) || res.Status.Code != 200)
        {
            future.ContinueWith(t => ctx.CloseAsync());
        }
    }

    /// <summary>
    /// 获取WebSocket位置
    /// </summary>
    // private static string GetWebSocketLocation(IFullHttpRequest req)
    // {
    //     var location = req.Headers.Get(AsciiString.Cached("Host"))?.ToString() + req.Uri;
    //     return "ws://" + location;
    // }

    private void HandleTextFrame(TextWebSocketFrame frame)
    {
        try
        {
            string text = frame.Text();
            _logger.LogDebug("Received text frame: {Text}", text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling text frame");
        }
    }

    private void HandleBinaryFrame(BinaryWebSocketFrame frame, List<object> output)
    {
        try
        {
            var buffer = frame.Content;

            if (_pbPacket == null)
            {
                int ptCode = ByteOrderHelper.ReadInt(buffer, ByteOrderHelper.ByteOrder.BigEndian);
                long index = ByteOrderHelper.ReadLong(buffer, ByteOrderHelper.ByteOrder.BigEndian);
                
                byte[] data = new byte[buffer.ReadableBytes];
                buffer.GetBytes(buffer.ReaderIndex, data);

                Packet packet = new Packet(ptCode, index, data);
                output.Add(packet);
            }
            else
            {
                byte[] data = new byte[buffer.ReadableBytes];
                buffer.GetBytes(buffer.ReaderIndex, data);
                IMessage message = _pbPacket.Descriptor.Parser.ParseFrom(data);
                output.Add(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling binary frame");
        }
    }

    private void HandlePingFrame(IChannelHandlerContext ctx, PingWebSocketFrame frame)
    {
        try
        {
            // 响应 Ping 帧
            var pongFrame = new PongWebSocketFrame(frame.Content.Copy());
            ctx.WriteAndFlushAsync(pongFrame);
            _logger.LogDebug("Responded to ping frame");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling ping frame");
        }
    }

    private void HandlePongFrame(PongWebSocketFrame frame)
    {
        try
        {
            _logger.LogDebug("Received pong frame");
            // Pong 帧通常用于心跳检测，这里可以更新连接的最后活跃时间
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling pong frame");
        }
    }

    private void HandleCloseFrame(IChannelHandlerContext ctx, CloseWebSocketFrame frame)
    {
        try
        {
            _logger.LogInformation("Received close frame with status: {Status}, reason: {Reason}",
                frame.StatusCode(), frame.ReasonText());
            
            // var connectionIdObj = ctx.GetAttribute(NetConstants.ConnectionIdKey).Get();
            // if (connectionIdObj is long connectionId)
            // {
            //     var connection = _connectionManager.GetConnection(connectionId);
            //     if (connection != null)
            //     {
            //         //关闭连接
            //         connection.Disconnect();
            //     }
            // }  todo 待优化，后续看怎么把关闭消息通知到具体的连接对象
            
            ctx.CloseAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling close frame");
        }
    }
}