using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using GameNetLib.Utils;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Codec;

/// <summary>
/// GyFrame 将指定的Packet结构作为一个整体解码
/// </summary>
public class GyTCPDecoder : ByteToMessageDecoder
{
    private readonly ILogger<GyTCPDecoder> _logger;

    /// <summary>
    /// 是否启用加密
    /// </summary>
    public static bool CanEncrypt { get; set; } = false;

    /// <summary>
    /// 是否启用压缩
    /// </summary>
    public static bool CanZip { get; set; } = true;

    /// <summary>
    /// 魔术包头
    /// </summary>
    public static readonly byte[] MagicHeader = System.Text.Encoding.UTF8.GetBytes("magic");

    /// <summary>
    /// 包头长度：4（包体长度）+ 5（magicHeader）+ 4（包体长度）+ 1（压缩标记）+ 1（加密标记）
    /// </summary>
    private static readonly int HeaderLength = 4 + MagicHeader.Length + 4 + 1 + 1;

    public GyTCPDecoder(ILogger<GyTCPDecoder> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override void Decode(IChannelHandlerContext context, IByteBuffer input, List<object> output)
    {
        try
        {
            // 缓冲区中可能包含了多个消息包，需要循环读取，在读取失败的时候退出，设置count防止死循环
            int count = 100;
            while (count-- > 0)
            {
                // 记录当前读取位置
                input.MarkReaderIndex();

                // 判断缓冲区可读数据是否能完整读取一个包体长度
                if (input.ReadableBytes < HeaderLength)
                {
                    // 不能或者读完包体长度无法继续读取包体，本次读取失败，不做任何读取，直接返回，等待下次调用
                    input.ResetReaderIndex();
                    return;
                }

                {
                    // 按顺序读取相应内容（使用大端字节序）
                    int bodyLength = input.ReadInt(ByteOrderHelper.ProtocolByteOrder); // 包体长度
                    for (int i = 0; i < MagicHeader.Length; i++) // 魔术包头
                    {
                        input.ReadByte();
                    }
                    input.ReadInt(ByteOrderHelper.ProtocolByteOrder); // 包体长度
                    if (bodyLength > 1024 * 1024 * 2) // 包体超过2M时认为数据错误，直接断开连接
                    {
                        _logger.LogError($"bad packet length : {bodyLength}, ip : {context.Channel.RemoteAddress}");
                        context.CloseAsync();
                        return;
                    }
                    if (bodyLength < 0)
                    {
                        _logger.LogError($"bodyLength is negative number length : {bodyLength}, ip : {context.Channel.RemoteAddress}");
                        context.CloseAsync();
                        return;
                    }
                    // 接下来要读取 压缩标记+是否加密+包体+字节m+字节a
                    if (input.ReadableBytes < bodyLength + 4)
                    {
                        // 不够读取接下来的内容，重置读取位置，返回，等待下次调用
                        input.ResetReaderIndex();
                        return;
                    }
                    else
                    {
                        byte compress = input.ReadByte(); // 压缩标记
                        byte isMagic = input.ReadByte(); // 加密标记

                        IByteBuffer bodyBuf = input.ReadBytes(bodyLength); // 包体
                        byte[] bytes = new byte[bodyLength];
                        bodyBuf.GetBytes(0, bytes);
                        bodyBuf.Release();

                        input.ReadByte(); // 字节m
                        input.ReadByte(); // 字节a

                        if (isMagic == 1) // 解密
                        {
                            // 简化的解密逻辑，实际项目中需要实现 ProtocolEncryptUtil
                            // bytes = ProtocolEncryptUtil.Decrypt(bytes);
                            _logger.LogDebug("Packet decryption requested but not implemented");
                        }

                        if (compress == 1) // 解压缩
                        {
                            // 简化的解压缩逻辑，实际项目中需要实现 CompressorUtil
                            // bytes = CompressorUtil.Deflate().Decompress(bytes);
                            _logger.LogDebug("Packet decompression requested but not implemented");
                        }

                        if (bytes.Length > 0)
                        {
                            IByteBuffer o = Unpooled.Buffer(bytes.Length);
                            o.WriteBytes(bytes, 0, bytes.Length);
                            output.Add(o);
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during packet decoding");
            throw;
        }
    }
}