using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Codec;

/// <summary>
/// Protobuf 解码器，将字节数据转换为 Protobuf 对象
/// </summary>
public class ProtobufDecoder : MessageToMessageDecoder<IByteBuffer>
{
    private readonly ILogger<ProtobufDecoder> _logger;
    private readonly IMessage _protoType;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ProtobufDecoder(ILogger<ProtobufDecoder> logger, IMessage protoType)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _protoType = protoType ?? throw new ArgumentNullException(nameof(protoType));
    }

    /// <summary>
    /// 解码方法，将 IByteBuffer 转换为 Protobuf 对象
    /// </summary>
    /// <param name="context">通道处理器上下文</param>
    /// <param name="message">输入的字节缓冲区</param>
    /// <param name="output">输出对象列表</param>
    protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
    {
        try
        {
            // 检查输入是否有效
            if (message == null || message.ReadableBytes == 0)
            {
                _logger.LogWarning("Received empty or null message buffer");
                return;
            }

            // 将 IByteBuffer 转换为 byte 数组
            byte[] data = new byte[message.ReadableBytes];
            message.GetBytes(message.ReaderIndex, data);

            // 解析为 PbPacket
            var packet = _protoType.Descriptor.Parser.ParseFrom(data);
            if (packet != null)
            {
                output.Add(packet);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decode protobuf message");
            // 可以选择抛出异常或者忽略错误的消息
            // throw;
        }
    }

}