using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using GameNetLib.Utils;
using Google.Protobuf;
using Microsoft.Extensions.Logging;

namespace GameNetLib.Codec;

/// <summary>
/// GyFrame 将指定的Packet结构作为一个整体编码
/// </summary>
public class GyTCPEncoder : MessageToByteEncoder<IMessage>
{
    private readonly ILogger<GyTCPEncoder> _logger;

    /// <summary>
    /// 是否启用加密
    /// </summary>
    public static bool CanEncrypt { get; set; } = false;

    /// <summary>
    /// 是否启用压缩
    /// </summary>
    public static bool CanZip { get; set; } = true;

    /// <summary>
    /// 魔术包头
    /// </summary>
    private static readonly byte[] MagicHeader = System.Text.Encoding.UTF8.GetBytes("magic");

    /// <summary>
    /// 包头长度：4（包体长度）+ 5（magicHeader）+ 4（包体长度）+ 1（压缩标记）+ 1（加密标记）
    /// </summary>
    private static readonly int HeaderLength = 4 + MagicHeader.Length + 4 + 1 + 1;

    public GyTCPEncoder(ILogger<GyTCPEncoder> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override void Encode(IChannelHandlerContext context, IMessage message, IByteBuffer output)
    {
        //转bytes
        byte[] array = message.ToByteArray();
        
        // 先读取包体长度，用于计算是否压缩
        int bodyLength = array.Length;

        // 原始长度
        int originalBodyLength = bodyLength;

        // 长度超过1024则需要压缩
        bool compress = false; // 暂时禁用压缩

        if (compress)
        {
            // 压缩包体，重新获得包体长度
            // 简化的压缩逻辑，实际项目中需要实现 CompressorUtil
            // array = CompressorUtil.Deflate().Compress(array);
            bodyLength = array.Length;
            _logger.LogDebug("Packet compression requested but not implemented");
        }

        // 确保缓冲区的长度：包头长度+包体长度+ ptcode + time + 2字节（最后的字节m和字节a）
        output.EnsureWritable(HeaderLength + bodyLength + 8 + 8 + 2);

        // 依次向缓冲区写入数据（使用大端字节序）
        output.WriteInt(bodyLength, ByteOrderHelper.ProtocolByteOrder); // 包体长度
        output.WriteBytes(MagicHeader); // 魔术包头
        output.WriteInt(originalBodyLength, ByteOrderHelper.ProtocolByteOrder); // 包体长度
        output.WriteByte(compress ? (byte)1 : (byte)0); // 压缩标记
        output.WriteByte(CanEncrypt ? (byte)1 : (byte)0); // 加密标记

        // 写入包体（加密或原始）
        if (CanEncrypt)
        {
            // 简化的加密逻辑，实际项目中需要实现 ProtocolEncryptUtil
            // var encryptedArray = ProtocolEncryptUtil.Encrypt(array);
            output.WriteBytes(array); // 暂时写入原始数据
            _logger.LogDebug("Packet encryption requested but not implemented");
        }
        else
        {
            output.WriteBytes(array); // 原始包体
        }
        
        output.WriteByte(MagicHeader[0]); // 字节m
        output.WriteByte(MagicHeader[1]); // 字节a
    }
}