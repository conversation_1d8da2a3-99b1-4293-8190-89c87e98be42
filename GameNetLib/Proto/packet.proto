syntax = "proto3";

package gamenetlib;

option csharp_namespace = "GameNetLib.Proto";

// 统一封包结构
message PbPacket {
  int32 cmd = 1;        // 命令ID
  bytes payload = 2;    // 消息载荷
}

// 心跳包
message HeartbeatPacket {
  int64 timestamp = 1;  // 时间戳
  string client_id = 2; // 客户端ID
}

// 认证包
message AuthPacket {
  string token = 1;     // 认证令牌
  string client_id = 2; // 客户端ID
  int32 version = 3;    // 协议版本
}

// 认证响应包
message AuthResponsePacket {
  bool success = 1;     // 认证是否成功
  string message = 2;   // 响应消息
  string session_id = 3; // 会话ID
}

// 错误包
message ErrorPacket {
  int32 error_code = 1; // 错误码
  string error_message = 2; // 错误消息
}

// 通用响应包
message ResponsePacket {
  int32 request_id = 1; // 请求ID
  bool success = 2;     // 是否成功
  bytes data = 3;       // 响应数据
  string message = 4;   // 响应消息
}
