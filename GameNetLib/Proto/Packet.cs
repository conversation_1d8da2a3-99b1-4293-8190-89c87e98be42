namespace GameNetLib.Proto;

public class Packet
{
    public int _PTCode { get; set; }
    public byte[] _Data { get; set; }
    public long _sn { get; set; }
    
    public Packet(int ptCode, long sn, byte[] data)
    {
        _PTCode = ptCode;
        _Data = data;
        _sn = sn;
    }
    
    public Packet()
    {
    }
    
    //reset packet
    public void Reset()
    {
        _PTCode = 0;
        _Data = null;
        _sn = 0;
    }
}